<template>
  <div class="occupy-form">
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
            基本信息
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerBasicForm" :schemas="basicInfoSchema" />
        </div>
      </div>

      <!-- 占用信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:user-check-outlined" class="title-icon" />
            占用信息
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerOccupyForm" :schemas="occupyInfoSchema" />
        </div>
      </div>

      <!-- 盘活记录 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:sync-outlined" class="title-icon" />
            盘活记录
          </div>
          <div class="form-card-action">
            <a-button type="primary" size="small" @click="addDealRecord">
              <Icon icon="ant-design:plus-outlined" />
              新增盘活记录
            </a-button>
          </div>
        </div>
        <div class="form-card-body">
          <a-table :dataSource="dealList" :columns="dealColumns" :pagination="false" :scroll="{ x: 1200 }" size="small" bordered>
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'date'">
                <a-date-picker
                  v-model:value="record.date"
                  placeholder="请选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </template>
              <template v-else-if="column.key === 'isResult'">
                <a-select v-model:value="record.isResult" placeholder="请选择" style="width: 100%">
                  <a-select-option :value="0">否</a-select-option>
                  <a-select-option :value="1">是</a-select-option>
                </a-select>
              </template>
              <template v-else-if="column.key === 'vitalizeType'">
                <a-select v-model:value="record.vitalizeType" placeholder="请选择盘活方式" style="width: 100%">
                  <a-select-option :value="0">出租</a-select-option>
                  <a-select-option :value="1">出售</a-select-option>
                  <a-select-option :value="2">资产证券化</a-select-option>
                  <a-select-option :value="3">收储</a-select-option>
                  <a-select-option :value="4">转为自用</a-select-option>
                  <a-select-option :value="5">转为借用</a-select-option>
                  <a-select-option :value="6">转为占用</a-select-option>
                </a-select>
              </template>
              <template v-else-if="column.key === 'reason'">
                <a-textarea v-model:value="record.reason" placeholder="请输入已采取的盘活管理措施" :rows="2" />
              </template>
              <template v-else-if="column.key === 'nextReason'">
                <a-textarea v-model:value="record.nextReason" placeholder="请输入下一步建议" :rows="2" />
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button type="link" danger size="small" @click="removeDealRecord(index)">
                  <Icon icon="ant-design:delete-outlined" />
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-footer">
        <a-button @click="handleReset">重置</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading" style="margin-left: 12px"> 提交 </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="OccupyForm" setup>
  import { ref, onMounted, computed, watch, h } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { basicInfoSchema, occupyInfoSchema } from './occupyForm.data';
  import { saveOrUpdate, getDetail } from './occupy.api';

  const route = useRoute();
  const router = useRouter();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');
  const originalStatus = ref<number | null>(null);

  // 盘活记录列表
  const dealList = ref<any[]>([]);

  // 盘活记录表格列定义
  const dealColumns = [
    {
      title: '日期',
      key: 'date',
      width: 150,
      customTitle: () => h('span', [
        h('span', { style: { color: '#f5222d' } }, '*'),
        ' 日期'
      ]),
    },
    {
      title: '是否已盘活',
      key: 'isResult',
      width: 110,
      customTitle: () => h('span', [
        h('span', { style: { color: '#f5222d' } }, '*'),
        ' 是否已盘活'
      ]),
    },
    {
      title: '盘活方式',
      key: 'vitalizeType',
      width: 130,
      customTitle: () => h('span', [
        h('span', { style: { color: '#f5222d' } }, '*'),
        ' 盘活方式'
      ]),
    },
    {
      title: '已采取的盘活管理措施',
      key: 'reason',
      minWidth: 300,
    },
    {
      title: '下一步建议',
      key: 'nextReason',
      minWidth: 300,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
    },
  ];

  // 注册表单
  const [
    registerBasicForm,
    { resetFields: resetBasicFields, setFieldsValue: setBasicFieldsValue, validate: validateBasic, getFieldsValue: getBasicFieldsValue },
  ] = useForm({
    labelWidth: 180,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: basicInfoSchema,
  });

  const [
    registerOccupyForm,
    { resetFields: resetOccupyFields, setFieldsValue: setOccupyFieldsValue, validate: validateOccupy, getFieldsValue: getOccupyFieldsValue },
  ] = useForm({
    labelWidth: 180,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: occupyInfoSchema,
  });

  // 计算属性：状态控制（暂时未使用，保留以备后续功能扩展）
  const _isDraftDisabled = computed(() => isUpdate.value);
  const _isFiledDisabled = computed(() => {
    if (!isUpdate.value) return false;
    return originalStatus.value !== 0;
  });
  const _isRevokedDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value !== 1;
  });
  const _isVoidDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value === null || ![0, 2].includes(originalStatus.value);
  });

  // 监听日期变化，自动计算占用天数
  watch(
    () => {
      const fields = getOccupyFieldsValue();
      return [fields?.startDate, fields?.endDate];
    },
    ([startDate, endDate]) => {
      calculateOccupyDays(startDate, endDate);
    }
  );

  // 初始化
  onMounted(async () => {
    // 判断是新增还是编辑
    const path = route.path;
    if (path.includes('/edit/')) {
      isUpdate.value = true;
      recordId.value = route.params.id as string;
      await loadData();
    } else {
      isUpdate.value = false;
      resetFields();
      // 设置默认值
      setDefaultValues();
    }
  });

  // 设置默认值
  function setDefaultValues() {
    setBasicFieldsValue({
      status: 0, // 默认草稿状态
      entryClerk: '当前用户', // 录入人
      createTime: new Date().toISOString().split('T')[0], // 录入时间
      isChangePurpose: 0, // 默认否
      isIllegalConstruction: 0, // 默认否
    });
  }

  // 加载数据
  async function loadData() {
    try {
      const record = await getDetail(recordId.value);
      setFieldsValue(record);
      originalStatus.value = record.status;
      
      // 加载盘活记录
      if (record.dealList && record.dealList.length > 0) {
        dealList.value = record.dealList;
      }
    } catch (error) {
      createMessage.error('加载数据失败');
    }
  }

  // 设置表单值
  function setFieldsValue(data: any) {
    // 基本信息
    setBasicFieldsValue({
      id: data.id,
      type: data.type,
      name: data.name,
      code: data.code,
      occupyName: data.occupyName,
      manageUnit: data.manageUnit,
      reportOrNot: data.reportOrNot,
      operator: data.operator,
      entryClerk: data.entryClerk,
      createTime: data.createTime,
      status: data.status,
    });

    // 占用信息
    setOccupyFieldsValue({
      startDate: data.startDate,
      endDate: data.endDate,
      occupyDays: data.occupyDays,
      occupyArea: data.occupyArea,
      occupyPerson: data.occupyPerson,
      assetsAmount: data.assetsAmount,
      bookAmount: data.bookAmount,
      dateOfBookValue: data.dateOfBookValue,
      isChangePurpose: data.isChangePurpose,
      isIllegalConstruction: data.isIllegalConstruction,
      occupyReason: data.occupyReason,
      remark: data.remark,
    });
  }

  // 重置表单
  function resetFields() {
    resetBasicFields();
    resetOccupyFields();
    dealList.value = [];
  }

  // 计算占用天数
  function calculateOccupyDays(startDate: string, endDate: string) {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      setOccupyFieldsValue({ occupyDays: diffDays });
    } else if (startDate) {
      const start = new Date(startDate);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      setOccupyFieldsValue({ occupyDays: diffDays });
    } else {
      setOccupyFieldsValue({ occupyDays: '' });
    }
  }

  // 添加盘活记录
  function addDealRecord() {
    dealList.value.push({
      date: '',
      isResult: 0,
      vitalizeType: 0,
      reason: '',
      nextReason: '',
    });
  }

  // 删除盘活记录
  function removeDealRecord(index: number) {
    createConfirm({
      title: '确认删除',
      content: '确定要删除该盘活记录吗?',
      onOk: () => {
        dealList.value.splice(index, 1);
        createMessage.success('删除成功');
      },
    });
  }

  // 提交表单
  async function handleSubmit() {
    try {
      loading.value = true;
      
      // 验证所有表单
      const [basicData, occupyData] = await Promise.all([validateBasic(), validateOccupy()]);

      // 合并数据
      const formData = {
        ...basicData,
        ...occupyData,
        dealList: dealList.value,
      };

      // 如果是编辑模式，添加ID
      if (isUpdate.value) {
        formData.id = recordId.value;
      }

      await saveOrUpdate(formData, isUpdate.value);
      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      router.push('/assetsUse/occupy');
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 重置
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '确定要重置表单吗？所有已填写的数据将会清空',
      onOk: () => {
        resetFields();
        if (!isUpdate.value) {
          setDefaultValues();
        }
        createMessage.success('表单已重置');
      },
    });
  }
</script>

<style lang="less" scoped>
  .occupy-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }

        .form-card-action {
          display: flex;
          align-items: center;
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    // 盘活记录表格样式
    :deep(.ant-table-thead > tr > th) {
      background-color: #fafafa;
      font-weight: 500;
    }

    :deep(.ant-table-tbody > tr > td) {
      padding: 8px;
    }

    // 帮助文本样式
    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }
  }
</style> 