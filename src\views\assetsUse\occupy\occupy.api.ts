import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/biz/occupancy/page',
  save = '/mock/occupy/add',
  edit = '/mock/occupy/edit',
  delete = '/mock/occupy/delete',
  deleteBatch = '/mock/occupy/deleteBatch',

  importExcel = '/biz/occupancy/excel/import',
  exportXls = '/biz/occupancy/excel/export',
  downloadTemplate = '/mock/occupy/downloadTemplate',

  detail = '/mock/occupy/detail',
}

export const exportExcel = Api.exportXls;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail, params: { id } });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteOccupy = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteOccupy = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 导出
 * @param params
 */
export const exportOccupy = (params) => defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });
